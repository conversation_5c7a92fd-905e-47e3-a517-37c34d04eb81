"""
Energy Saving Sleep Hysteresis (ESSH) - LSTM Implementation
Based on the ESSH document analysis, this solution demonstrates:
1. Data preprocessing for cellular network traffic data
2. LSTM model for traffic forecasting
3. ESSH decision logic implementation
4. Energy savings calculation
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import torch
import torch.nn as nn
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error
import warnings
warnings.filterwarnings('ignore')

# Set random seeds for reproducibility
np.random.seed(42)
torch.manual_seed(42)

class ESShLSTM(nn.Module):
    """LSTM model for traffic forecasting in ESSH system"""
    
    def __init__(self, input_size=1, hidden_size=32, num_layers=2, output_size=3, dropout=0.2):
        super(ESShLSTM, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, 
                           batch_first=True, dropout=dropout)
        self.fc = nn.Linear(hidden_size, output_size)
        self.sigmoid = nn.Sigmoid()
        
    def forward(self, x):
        # Initialize hidden state
        h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size)
        c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size)
        
        # Forward propagate LSTM
        out, _ = self.lstm(x, (h0, c0))
        
        # Take the last output
        out = self.fc(out[:, -1, :])
        out = self.sigmoid(out)
        
        return out

class ESShDecisionEngine:
    """ESSH Decision Engine implementing the hysteresis logic"""
    
    def __init__(self, off_threshold=0.20, on_threshold=0.30, 
                 min_off_time=1, min_on_time=1, forecast_horizon=3):
        self.off_threshold = off_threshold
        self.on_threshold = on_threshold
        self.min_off_time = min_off_time
        self.min_on_time = min_on_time
        self.forecast_horizon = forecast_horizon
        
        # State tracking
        self.cell_states = {}  # cell_id -> 1 (ON) or 0 (OFF)
        self.min_off_timers = {}  # cell_id -> remaining bins
        self.min_on_timers = {}   # cell_id -> remaining bins
        
    def initialize_cell(self, cell_id):
        """Initialize a new cell in the system"""
        if cell_id not in self.cell_states:
            self.cell_states[cell_id] = 1  # Start ON
            self.min_off_timers[cell_id] = 0
            self.min_on_timers[cell_id] = 0
    
    def update_timers(self):
        """Update minimum ON/OFF timers for all cells"""
        for cell_id in self.cell_states:
            self.min_off_timers[cell_id] = max(0, self.min_off_timers[cell_id] - 1)
            self.min_on_timers[cell_id] = max(0, self.min_on_timers[cell_id] - 1)
    
    def make_decision(self, cell_id, forecast, current_kpis):
        """
        Make ON/OFF decision for a cell based on forecast and current KPIs
        
        Args:
            cell_id: Cell identifier
            forecast: Array of forecasted traffic values for next H periods
            current_kpis: Dict with current KPI values
            
        Returns:
            tuple: (new_state, action, reason)
        """
        self.initialize_cell(cell_id)
        
        current_state = self.cell_states[cell_id]
        max_forecast = np.max(forecast)
        
        # Check KPI health
        kpi_healthy = (current_kpis.get('drop_rate', 0) < 0.05 and 
                      current_kpis.get('RRC_fail', 0) < 0.02 and
                      current_kpis.get('ERAB_fail', 0) < 0.01)
        
        if current_state == 1:  # Currently ON
            # Can turn OFF if forecast is low, KPIs are good, and minimum ON time satisfied
            if (max_forecast < self.off_threshold and 
                kpi_healthy and 
                self.min_on_timers[cell_id] == 0):
                
                self.cell_states[cell_id] = 0
                self.min_off_timers[cell_id] = self.min_off_time
                return 0, "TURN_OFF", f"Forecast max {max_forecast:.3f} < threshold {self.off_threshold}"
            else:
                return 1, "KEEP_ON", "Conditions not met for turning OFF"
                
        else:  # Currently OFF
            # Must turn ON if forecast is high or minimum OFF time satisfied
            if (max_forecast > self.on_threshold and 
                self.min_off_timers[cell_id] == 0):
                
                self.cell_states[cell_id] = 1
                self.min_on_timers[cell_id] = self.min_on_time
                return 1, "TURN_ON", f"Forecast max {max_forecast:.3f} > threshold {self.on_threshold}"
            else:
                return 0, "KEEP_OFF", "Conditions not met for turning ON"

def load_and_preprocess_data(file_path):
    """Load and preprocess the ESSH data"""
    df = pd.read_csv(file_path)
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    df = df.sort_values(['cell_id', 'timestamp'])
    
    # Normalize traffic data to [0,1] range
    scaler = MinMaxScaler()
    df['users_active_norm'] = scaler.fit_transform(df[['users_active']])
    df['PRB_util_norm'] = df['PRB_util_DL']  # Already in [0,1] range
    
    return df, scaler

def create_sequences(data, seq_length=4, forecast_horizon=3):
    """Create sequences for LSTM training"""
    X, y = [], []
    
    for i in range(len(data) - seq_length - forecast_horizon + 1):
        # Input sequence
        X.append(data[i:(i + seq_length)])
        # Target: next forecast_horizon values
        y.append(data[(i + seq_length):(i + seq_length + forecast_horizon)])
    
    return np.array(X), np.array(y)

def train_lstm_model(X_train, y_train, epochs=50, batch_size=32):
    """Train the LSTM model"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Convert to tensors
    X_train_tensor = torch.FloatTensor(X_train).unsqueeze(-1).to(device)
    y_train_tensor = torch.FloatTensor(y_train).to(device)
    
    # Initialize model
    model = ESShLSTM(input_size=1, hidden_size=32, num_layers=2, output_size=3)
    model = model.to(device)
    
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    
    # Training loop
    model.train()
    for epoch in range(epochs):
        total_loss = 0
        
        for i in range(0, len(X_train_tensor), batch_size):
            batch_X = X_train_tensor[i:i+batch_size]
            batch_y = y_train_tensor[i:i+batch_size]
            
            optimizer.zero_grad()
            outputs = model(batch_X)
            loss = criterion(outputs, batch_y)
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
        
        if (epoch + 1) % 10 == 0:
            print(f'Epoch [{epoch+1}/{epochs}], Loss: {total_loss/len(X_train_tensor):.6f}')
    
    return model

def simulate_essh_system(df, model, decision_engine):
    """Simulate the ESSH system operation"""
    results = []
    
    # Group by cell for processing
    for cell_id in df['cell_id'].unique():
        cell_data = df[df['cell_id'] == cell_id].copy()
        cell_data = cell_data.sort_values('timestamp')
        
        traffic_data = cell_data['users_active_norm'].values
        
        for i in range(4, len(cell_data)):  # Start after sequence length
            # Get historical sequence
            sequence = traffic_data[i-4:i]
            
            # Make forecast using LSTM
            with torch.no_grad():
                seq_tensor = torch.FloatTensor(sequence).unsqueeze(0).unsqueeze(-1)
                forecast = model(seq_tensor).numpy().squeeze()
            
            # Current KPIs
            current_kpis = {
                'drop_rate': cell_data.iloc[i]['drop_rate'],
                'RRC_fail': cell_data.iloc[i]['RRC_fail'],
                'ERAB_fail': cell_data.iloc[i]['ERAB_fail']
            }
            
            # Make decision
            new_state, action, reason = decision_engine.make_decision(
                cell_id, forecast, current_kpis
            )
            
            # Calculate energy consumption
            energy = 850 if new_state == 1 else 50  # ON vs OFF power consumption
            
            results.append({
                'timestamp': cell_data.iloc[i]['timestamp'],
                'cell_id': cell_id,
                'actual_traffic': cell_data.iloc[i]['users_active_norm'],
                'forecast_max': np.max(forecast),
                'cell_state': new_state,
                'action': action,
                'reason': reason,
                'energy_W': energy,
                'drop_rate': current_kpis['drop_rate']
            })
        
        # Update timers after processing each time step
        decision_engine.update_timers()
    
    return pd.DataFrame(results)

def calculate_energy_savings(results_df):
    """Calculate energy savings metrics"""
    total_time_bins = len(results_df)
    
    # Baseline: all cells always ON
    baseline_energy = total_time_bins * 850
    
    # ESSH energy consumption
    essh_energy = results_df['energy_W'].sum()
    
    # Savings
    energy_saved = baseline_energy - essh_energy
    pct_saved = (energy_saved / baseline_energy) * 100
    
    # OFF time statistics
    off_time_pct = (results_df['cell_state'] == 0).mean() * 100
    
    return {
        'baseline_energy_W': baseline_energy,
        'essh_energy_W': essh_energy,
        'energy_saved_W': energy_saved,
        'percent_saved': pct_saved,
        'off_time_percent': off_time_pct,
        'avg_drop_rate': results_df['drop_rate'].mean()
    }

# Main execution
if __name__ == "__main__":
    print("=== Energy Saving Sleep Hysteresis (ESSH) Demo ===\n")
    
    # Load and preprocess data
    print("1. Loading and preprocessing data...")
    df, scaler = load_and_preprocess_data('essh_simulated_data.csv')
    print(f"Loaded {len(df)} records for {df['cell_id'].nunique()} cells")
    print(f"Data shape: {df.shape}")
    print(f"Columns: {list(df.columns)}")
    
    # Prepare training data (using users_active_norm as target)
    print("\n2. Preparing training sequences...")
    traffic_data = df['users_active_norm'].values
    X, y = create_sequences(traffic_data, seq_length=4, forecast_horizon=3)
    print(f"Training sequences: X shape {X.shape}, y shape {y.shape}")
    
    # Train LSTM model
    print("\n3. Training LSTM forecasting model...")
    model = train_lstm_model(X, y, epochs=20, batch_size=16)
    
    # Initialize decision engine
    print("\n4. Initializing ESSH decision engine...")
    decision_engine = ESShDecisionEngine(
        off_threshold=0.20,
        on_threshold=0.30,
        min_off_time=1,
        min_on_time=1
    )
    
    # Simulate ESSH system
    print("\n5. Running ESSH simulation...")
    results = simulate_essh_system(df, model, decision_engine)
    print(f"Simulation completed. Generated {len(results)} decision points.")
    
    # Calculate metrics
    print("\n6. Calculating energy savings metrics...")
    metrics = calculate_energy_savings(results)
    
    print("\n=== ESSH RESULTS ===")
    for key, value in metrics.items():
        if 'percent' in key or 'pct' in key:
            print(f"{key}: {value:.2f}%")
        elif 'energy' in key or 'W' in key:
            print(f"{key}: {value:.0f} W")
        else:
            print(f"{key}: {value:.4f}")
    
    # Display sample results
    print("\n=== SAMPLE DECISIONS ===")
    print(results[['timestamp', 'cell_id', 'actual_traffic', 'forecast_max', 
                  'cell_state', 'action', 'energy_W']].head())
    
    # Visualization
    print("\n7. Creating visualizations...")
    
    # Plot 1: Traffic vs Forecast
    plt.figure(figsize=(12, 8))
    
    plt.subplot(3, 1, 1)
    plt.plot(results['actual_traffic'].values, label='Actual Traffic', alpha=0.7)
    plt.plot(results['forecast_max'].values, label='Forecast Max', alpha=0.7)
    plt.axhline(y=0.20, color='r', linestyle='--', label='OFF Threshold')
    plt.axhline(y=0.30, color='g', linestyle='--', label='ON Threshold')
    plt.title('Traffic Forecast vs Actual')
    plt.ylabel('Normalized Traffic')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Plot 2: Cell State
    plt.subplot(3, 1, 2)
    plt.plot(results['cell_state'].values, label='Cell State (1=ON, 0=OFF)', linewidth=2)
    plt.title('Cell ON/OFF State Over Time')
    plt.ylabel('State')
    plt.ylim(-0.1, 1.1)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Plot 3: Energy Consumption
    plt.subplot(3, 1, 3)
    plt.plot(results['energy_W'].values, label='Energy Consumption', color='orange')
    plt.title('Energy Consumption Over Time')
    plt.ylabel('Power (W)')
    plt.xlabel('Time Bins (15-min intervals)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # Save results
    results.to_csv('essh_simulation_results.csv', index=False)
    print(f"\nResults saved to 'essh_simulation_results.csv'")
    
    return model, results, metrics

# Additional utility functions for production deployment
def validate_forecast_accuracy(model, X_test, y_test):
    """Validate LSTM forecast accuracy"""
    model.eval()
    with torch.no_grad():
        X_test_tensor = torch.FloatTensor(X_test).unsqueeze(-1)
        predictions = model(X_test_tensor).numpy()
    
    mse = mean_squared_error(y_test.flatten(), predictions.flatten())
    mae = mean_absolute_error(y_test.flatten(), predictions.flatten())
    
    return {'MSE': mse, 'MAE': mae, 'RMSE': np.sqrt(mse)}

def generate_extended_dataset(num_cells=5, days=7, bins_per_day=96):
    """Generate extended synthetic dataset for more comprehensive testing"""
    all_data = []
    
    for cell_idx in range(num_cells):
        cell_id = f"CELL_{cell_idx+1:03d}"
        
        for day in range(days):
            base_date = pd.Timestamp('2024-01-01') + pd.Timedelta(days=day)
            
            for bin_idx in range(bins_per_day):
                timestamp = base_date + pd.Timedelta(minutes=15*bin_idx)
                
                # Generate realistic daily traffic pattern
                hour_of_day = timestamp.hour + timestamp.minute/60
                daily_pattern = 0.3 + 0.4 * np.sin(2 * np.pi * (hour_of_day - 6) / 24)
                
                # Add cell-specific variation
                cell_factor = 0.8 + 0.4 * (cell_idx / num_cells)
                
                # Add noise
                noise = np.random.normal(0, 0.05)
                
                users_active = max(0, min(100, int(100 * (daily_pattern * cell_factor + noise))))
                PRB_util = max(0, min(1, daily_pattern * cell_factor + noise))
                
                # Generate correlated KPIs
                base_drop_rate = 0.01 + 0.02 * PRB_util
                drop_rate = max(0, base_drop_rate + np.random.normal(0, 0.005))
                
                all_data.append({
                    'timestamp': timestamp,
                    'cell_id': cell_id,
                    'users_active': users_active,
                    'PRB_util_DL': PRB_util,
                    'drop_rate': drop_rate,
                    'RRC_fail': max(0, drop_rate * 0.5 + np.random.normal(0, 0.002)),
                    'ERAB_fail': max(0, drop_rate * 0.3 + np.random.normal(0, 0.001)),
                    'power_W': 850 + np.random.normal(0, 20),
                    'neighbor_spare_capacity': 0.25 + np.random.normal(0, 0.05),
                    'time_of_day': hour_of_day,
                    'event_flag': 1 if np.random.random() < 0.02 else 0
                })
    
    return pd.DataFrame(all_data)

if __name__ == "__main__":
    # Run the demo with the provided 10-row dataset
    model, results, metrics = main()
