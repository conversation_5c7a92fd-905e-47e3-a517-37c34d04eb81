"""
Simple ESSH Demo with 10-row dataset
This demonstrates the core concepts with minimal data
"""

import numpy as np
import pandas as pd
import torch
import torch.nn as nn

# Load the simulated data
print("Loading simulated ESSH data...")
df = pd.read_csv('essh_simulated_data.csv')
print("Dataset:")
print(df)
print(f"\nDataset shape: {df.shape}")

# Key ESSH parameters from the document
OFF_THRESHOLD = 0.20  # 20% of cell capacity
ON_THRESHOLD = 0.30   # 30% of cell capacity
FORECAST_HORIZON = 3  # 45 minutes (3 x 15-min bins)
SEQUENCE_LENGTH = 4   # 60 minutes of history

print(f"\nESSH Parameters:")
print(f"OFF Threshold: {OFF_THRESHOLD}")
print(f"ON Threshold: {ON_THRESHOLD}")
print(f"Forecast Horizon: {FORECAST_HORIZON} bins (45 minutes)")
print(f"Sequence Length: {SEQUENCE_LENGTH} bins (60 minutes)")

# Simple LSTM model for demonstration
class SimpleLSTM(nn.Module):
    def __init__(self, input_size=1, hidden_size=16, output_size=3):
        super(SimpleLSTM, self).__init__()
        self.lstm = nn.LSTM(input_size, hidden_size, batch_first=True)
        self.fc = nn.Linear(hidden_size, output_size)
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x):
        lstm_out, _ = self.lstm(x)
        output = self.fc(lstm_out[:, -1, :])  # Take last output
        return self.sigmoid(output)

# Prepare data for LSTM
print(f"\nPreparing data for LSTM...")

# Use PRB utilization as the main traffic indicator
traffic_data = df['PRB_util_DL'].values
print(f"Traffic data: {traffic_data}")

# For demonstration with limited data, we'll create a simple forecast
# In production, you'd need much more historical data
def simple_forecast_demo(current_sequence, horizon=3):
    """
    Simple forecasting demo - in production, use trained LSTM
    This uses trend analysis for demonstration
    """
    if len(current_sequence) < 2:
        return np.full(horizon, current_sequence[-1])
    
    # Simple trend-based forecast
    trend = current_sequence[-1] - current_sequence[-2]
    forecast = []
    last_val = current_sequence[-1]
    
    for i in range(horizon):
        next_val = max(0, min(1, last_val + trend * 0.5))  # Damped trend
        forecast.append(next_val)
        last_val = next_val
    
    return np.array(forecast)

# ESSH Decision Logic
class ESShDecisionDemo:
    def __init__(self):
        self.cell_state = 1  # Start ON
        self.min_off_timer = 0
        self.min_on_timer = 0
        self.decisions = []
    
    def make_decision(self, forecast, current_kpis, timestamp):
        # Update timers
        self.min_off_timer = max(0, self.min_off_timer - 1)
        self.min_on_timer = max(0, self.min_on_timer - 1)
        
        max_forecast = np.max(forecast)
        
        # Check KPI health
        kpi_healthy = (current_kpis['drop_rate'] < 0.05 and 
                      current_kpis['RRC_fail'] < 0.02)
        
        action = "NO_CHANGE"
        reason = ""
        
        if self.cell_state == 1:  # Currently ON
            if (max_forecast < OFF_THRESHOLD and 
                kpi_healthy and 
                self.min_on_timer == 0):
                self.cell_state = 0
                self.min_off_timer = 1  # Minimum 1 bin OFF
                action = "TURN_OFF"
                reason = f"Forecast {max_forecast:.3f} < {OFF_THRESHOLD}"
            else:
                action = "STAY_ON"
                reason = "Conditions not met for OFF"
        
        else:  # Currently OFF
            if max_forecast > ON_THRESHOLD and self.min_off_timer == 0:
                self.cell_state = 1
                self.min_on_timer = 1  # Minimum 1 bin ON
                action = "TURN_ON"
                reason = f"Forecast {max_forecast:.3f} > {ON_THRESHOLD}"
            else:
                action = "STAY_OFF"
                reason = "Conditions not met for ON"
        
        # Calculate energy
        energy = 850 if self.cell_state == 1 else 50
        
        decision = {
            'timestamp': timestamp,
            'forecast': forecast,
            'max_forecast': max_forecast,
            'current_state': self.cell_state,
            'action': action,
            'reason': reason,
            'energy_W': energy,
            'kpi_healthy': kpi_healthy
        }
        
        self.decisions.append(decision)
        return decision

# Run ESSH simulation
print(f"\nRunning ESSH simulation...")
decision_engine = ESShDecisionDemo()

# Process each time step (starting from index 3 to have enough history)
for i in range(3, len(df)):
    # Get sequence for forecasting
    sequence = traffic_data[max(0, i-3):i+1]  # Last 4 values
    
    # Make forecast
    forecast = simple_forecast_demo(sequence, FORECAST_HORIZON)
    
    # Current KPIs
    current_kpis = {
        'drop_rate': df.iloc[i]['drop_rate'],
        'RRC_fail': df.iloc[i]['RRC_fail'],
        'ERAB_fail': df.iloc[i]['ERAB_fail']
    }
    
    # Make decision
    decision = decision_engine.make_decision(
        forecast, current_kpis, df.iloc[i]['timestamp']
    )
    
    print(f"\nTime: {decision['timestamp']}")
    print(f"Sequence: {sequence}")
    print(f"Forecast: {forecast}")
    print(f"Max Forecast: {decision['max_forecast']:.3f}")
    print(f"Action: {decision['action']}")
    print(f"Reason: {decision['reason']}")
    print(f"Energy: {decision['energy_W']} W")

# Calculate summary metrics
decisions_df = pd.DataFrame(decision_engine.decisions)
total_energy_baseline = len(decisions_df) * 850  # All ON
total_energy_essh = decisions_df['energy_W'].sum()
energy_saved = total_energy_baseline - total_energy_essh
pct_saved = (energy_saved / total_energy_baseline) * 100

print(f"\n=== SUMMARY METRICS ===")
print(f"Total decisions: {len(decisions_df)}")
print(f"Baseline energy (all ON): {total_energy_baseline} W")
print(f"ESSH energy consumption: {total_energy_essh} W")
print(f"Energy saved: {energy_saved} W")
print(f"Percentage saved: {pct_saved:.1f}%")
print(f"Time spent OFF: {(decisions_df['current_state'] == 0).sum()} / {len(decisions_df)} bins")

# Save results
decisions_df.to_csv('essh_demo_results.csv', index=False)
print(f"\nResults saved to 'essh_demo_results.csv'")

print(f"\n=== NEXT STEPS FOR PRODUCTION ===")
print("1. Replace synthetic data with real cellular network KPI data")
print("2. Train LSTM on larger historical dataset (weeks/months)")
print("3. Implement proper validation and backtesting")
print("4. Add neighbor capacity modeling")
print("5. Implement watchdog mechanisms for KPI monitoring")
print("6. Add cluster-level constraints (max concurrent OFF)")
print("7. Implement progressive rollout strategy")
